package io.gigsta.data.network

object NetworkConfig {
    // TODO: Move these to build configuration or environment variables
    // These should match the values from your web app's .env file
    const val SUPABASE_URL = "https://your-project.supabase.co"
    const val SUPABASE_ANON_KEY = "your-anon-key"
    
    // For development, you might want to use local Supabase instance
    // const val SUPABASE_URL = "http://localhost:54321"
    // const val SUPABASE_ANON_KEY = "your-local-anon-key"
}
