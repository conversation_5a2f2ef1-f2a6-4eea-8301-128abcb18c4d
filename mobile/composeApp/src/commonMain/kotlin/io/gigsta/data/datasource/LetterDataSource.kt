package io.gigsta.data.datasource

import io.gigsta.data.model.LetterHistoryItem
import io.gigsta.data.network.SupabaseClient
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Columns

class LetterDataSource {
    
    private val supabase = SupabaseClient.client
    
    suspend fun getLetterHistory(userId: String): Result<List<LetterHistoryItem>> {
        return try {
            val result = supabase
                .from("letters")
                .select(columns = Columns.ALL) {
                    filter {
                        eq("user_id", userId)
                        eq("status", "done")
                        not("design_html", "is", null)
                        not("plain_text", "is", null)
                        neq("design_html", "")
                        neq("plain_text", "")
                    }
                    order("created_at", ascending = false)
                }
                .decodeList<LetterHistoryItem>()
            Result.success(result)
        } catch (e: Exception) {
            println("Error fetching letter history: ${e.message}")
            Result.failure(Exception("Failed to fetch letter history: ${e.message}"))
        }
    }
    
    suspend fun getLetterById(id: String): Result<LetterHistoryItem?> {
        return try {
            val result = supabase
                .from("letters")
                .select(columns = Columns.ALL) {
                    filter {
                        eq("id", id)
                    }
                }
                .decodeSingle<LetterHistoryItem>()
            Result.success(result)
        } catch (e: Exception) {
            println("Error fetching letter by id: ${e.message}")
            Result.failure(Exception("Failed to fetch letter: ${e.message}"))
        }
    }
}
