package io.gigsta.data.datasource

import io.gigsta.data.model.ResumeHistoryItem
import io.gigsta.data.network.SupabaseClient
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Columns
import io.github.jan.supabase.postgrest.query.Order
import io.github.jan.supabase.postgrest.query.filter.FilterOperator

class ResumeDataSource {
    
    private val supabase = SupabaseClient.client
    
    suspend fun getResumeHistory(userId: String): Result<List<ResumeHistoryItem>> {
        return try {
            val result = supabase
                .from("resumes")
                .select(columns = Columns.ALL) {
                    filter {
                        eq("user_id", userId)
                        eq("status", "done")
                        eq("tokens_deducted", true)
                        filterNot("structured_data", FilterOperator.IS, null)
                        filterNot("html", FilterOperator.IS, null)
                    }
                    order("created_at", Order.DESCENDING)
                }
                .decodeList<ResumeHistoryItem>()
            Result.success(result)
        } catch (e: Exception) {
            println("Error fetching resume history: ${e.message}")
            Result.failure(Exception("Failed to fetch resume history: ${e.message}"))
        }
    }
    
    suspend fun getResumeById(id: String): Result<ResumeHistoryItem?> {
        return try {
            val result = supabase
                .from("resumes")
                .select(columns = Columns.ALL) {
                    filter {
                        eq("id", id)
                    }
                }
                .decodeSingle<ResumeHistoryItem>()
            Result.success(result)
        } catch (e: Exception) {
            println("Error fetching resume by id: ${e.message}")
            Result.failure(Exception("Failed to fetch resume: ${e.message}"))
        }
    }
}
